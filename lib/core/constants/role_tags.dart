class RoleTags {
  static String dashboardOverview = "dashboard-overview";
  static String revenueAnalytics = "revenue-analytics";
  static String customerOverview = "customer-overview"; //Dashboard
  static String recentCustomers = "recent-customers";
  static String productOverview = "product-overview";
  static String newProducts = "new-products";
  static String vendorProfile = "vendor-profile";
  static String product = "product";
  static String sales = "sales";
  static String salesAnalytics = "sales-analytics";
  static String returns = "returns";
  static String customer = "customer"; //Module
  static String review = "review";
  static String discount = "discount";
  static String staff = "staff";
  static String reports = "reports";
}

class RowParams {
  static String sales = 'sales';
  static String inventory = 'inventory';
  static String discount = 'discount';
  static String staff = 'staff';
  static String reports = 'reports';
  static String customer = 'customer';
  static String product = 'product';
  static String review = 'review';
  static String returns = 'returns';
  static String vendorProfile = 'vendorProfile';
}
