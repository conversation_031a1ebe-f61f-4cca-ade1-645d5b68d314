// class AppTypography {
//   // Default color for all text styles

//   // Base method to create TextStyle with common properties
//   static TextStyle _createTextStyle({
//     required double fontSize,
//     FontWeight fontWeight = FontWeight.normal,
//     Color? color,
//     double? height,
//   }) {
//     return TextStyle(
//       fontWeight: fontWeight,
//       color: color,
//       fontSize: Sizer.text(fontSize),
//       height: height,
//     );
//   }

//   // Font size definitions
//   static TextStyle text12 = _createTextStyle(fontSize: 12);
//   static TextStyle text13 = _createTextStyle(fontSize: 13);
//   static TextStyle text14 = _createTextStyle(fontSize: 14);
//   static TextStyle text15 = _createTextStyle(fontSize: 15);
//   static TextStyle text16 = _createTextStyle(fontSize: 16);
//   static TextStyle text18 = _createTextStyle(fontSize: 18);
//   static TextStyle text20 = _createTextStyle(fontSize: 20);
//   static TextStyle text22 = _createTextStyle(fontSize: 22);
//   static TextStyle text24 = _createTextStyle(fontSize: 24);
//   static TextStyle text26 = _createTextStyle(fontSize: 26);
//   static TextStyle text28 = _createTextStyle(fontSize: 28);
//   static TextStyle text30 = _createTextStyle(fontSize: 30);
//   static TextStyle text32 = _createTextStyle(fontSize: 32);
//   static TextStyle text34 = _createTextStyle(fontSize: 34);
//   static TextStyle text36 = _createTextStyle(fontSize: 36);
//   static TextStyle text38 = _createTextStyle(fontSize: 38);
//   static TextStyle text40 = _createTextStyle(fontSize: 40);
// }
