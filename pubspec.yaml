name: builders_konnect
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  dio: ^5.8.0+1
  flutter_dotenv: ^5.2.1
  flutter_svg: ^2.2.0
  # skeletonizer: ^2.1.0+1
  skeletonizer: ^1.4.3
  flutter_screenutil: ^5.9.3
  cached_network_image: ^3.4.1
  iconsax: ^0.0.8
  currency_text_input_formatter: ^2.3.0
  intl: ^0.20.2
  pretty_dio_logger: ^1.4.0
  # loading_animation_widget: ^1.3.0
  collection: ^1.19.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_riverpod: ^2.6.1
  another_flushbar: ^1.12.30
  percent_indicator: ^4.2.5
  pinput: ^5.0.1
  image_picker: ^1.1.2
  image_cropper: ^9.1.0
  google_fonts: ^6.2.1
  card_swiper: ^3.0.1
  flutter_spinkit: ^5.2.1
  webview_flutter: ^4.13.0
  url_launcher: ^6.3.2
  file_picker: ^10.2.0
  path_provider: ^2.1.5
  path: ^1.9.0
  simple_barcode_scanner: ^0.1.1
  permission_handler: ^11.3.1
  flutter_rating: ^2.0.2
  flutter_html: ^3.0.0
  dotted_border: ^2.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  riverpod_lint: ^2.6.5
  custom_lint: ^0.7.5
  rename_app: ^1.6.3
  flutter_launcher_icons: ^0.14.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/svgs/

    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto/Roboto-Light.ttf
          weight: 300
        - asset: assets/fonts/Roboto/Roboto-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto/Roboto-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Roboto/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto/Roboto-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Roboto/Roboto-Black.ttf
          weight: 900

  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
